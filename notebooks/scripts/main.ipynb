import os
from collections import Counter
import socket
import time

import ray

RAY_HEAD = os.environ.get('RAY_MASTER')
REDIS_PASSWORD = os.environ.get('REDISPASSWORD')

print(ray.__version__)

ray.init(address=RAY_HEAD)

from ray.data.Datasource

print('''This cluster consists of
    {} nodes in total
    {} CPU resources in total
    {} GPUs in total
'''.format(len(ray.nodes()), ray.cluster_resources()['CPU'], ray.cluster_resources().get('GPU', 0)))

@ray.remote
def f():
    time.sleep(0.001)
    # Return IP address.
    return socket.gethostbyname(socket.gethostname())

object_ids = [f.remote() for _ in range(10000)]
ip_addresses = ray.get(object_ids)

print('Tasks executed')
for ip_address, num_tasks in Counter(ip_addresses).items():
    print('    {} tasks on {}'.format(num_tasks, ip_address))

ray.shutdown()